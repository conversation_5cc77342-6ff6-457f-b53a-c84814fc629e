"""
Performance Optimization Module
Provides query optimization, caching strategies, and performance monitoring.
"""

import time
import functools
from typing import Any, Dict, List, Optional, Callable
from sqlalchemy.orm import Session, joinedload, selectinload
from sqlalchemy import text, func
import logging
from contextlib import contextmanager

logger = logging.getLogger(__name__)

class QueryOptimizer:
    """Optimizes database queries to prevent N+1 problems and improve performance."""
    
    @staticmethod
    def get_scans_with_stats_optimized(db: Session, skip: int = 0, limit: int = 20):
        """Optimized scan query with eager loading to prevent N+1 queries."""
        return db.query(models.Scan)\
            .options(
                selectinload(models.Scan.player_stats)
                .selectinload(models.PlayerStat.player)
            )\
            .order_by(models.Scan.timestamp.desc())\
            .offset(skip)\
            .limit(limit)\
            .all()
    
    @staticmethod
    def get_performance_data_optimized(db: Session, baseline_scan_id: int, current_scan_id: int, limit: int = 100):
        """Optimized performance data query with minimal database hits."""
        # Use a single query with joins instead of multiple queries
        query = text("""
            SELECT 
                p.id as player_id,
                p.governor_id,
                p.name as player_name,
                p.alliance,
                current_stats.power as current_power,
                current_stats.total_kill_points as current_kp,
                current_stats.dead_troops as current_dead,
                baseline_stats.power as baseline_power,
                baseline_stats.total_kill_points as baseline_kp,
                baseline_stats.dead_troops as baseline_dead,
                (current_stats.power - COALESCE(baseline_stats.power, 0)) as power_delta,
                (current_stats.total_kill_points - COALESCE(baseline_stats.total_kill_points, 0)) as kp_delta,
                (current_stats.dead_troops - COALESCE(baseline_stats.dead_troops, 0)) as dead_delta,
                CASE 
                    WHEN baseline_stats.power IS NULL THEN 1 
                    ELSE 0 
                END as is_new_player
            FROM players p
            JOIN player_stats current_stats ON p.id = current_stats.player_id AND current_stats.scan_id = :current_scan_id
            LEFT JOIN player_stats baseline_stats ON p.id = baseline_stats.player_id AND baseline_stats.scan_id = :baseline_scan_id
            ORDER BY kp_delta DESC
            LIMIT :limit
        """)
        
        result = db.execute(query, {
            'current_scan_id': current_scan_id,
            'baseline_scan_id': baseline_scan_id,
            'limit': limit
        })
        
        return [dict(row) for row in result]
    
    @staticmethod
    def get_dashboard_totals_optimized(db: Session, scan_id: int) -> Dict[str, int]:
        """Get dashboard totals with a single aggregation query."""
        query = text("""
            SELECT 
                COUNT(*) as player_count,
                SUM(power) as total_power,
                SUM(total_kill_points) as total_kill_points,
                SUM(dead_troops) as total_dead_troops,
                SUM(kill_points_t4 + kill_points_t5) as total_t45_kills
            FROM player_stats ps
            WHERE ps.scan_id = :scan_id
        """)
        
        result = db.execute(query, {'scan_id': scan_id}).first()
        
        return {
            'player_count': result.player_count or 0,
            'total_power': result.total_power or 0,
            'total_kill_points': result.total_kill_points or 0,
            'total_dead_troops': result.total_dead_troops or 0,
            'total_t45_kills': result.total_t45_kills or 0
        }


class PerformanceMonitor:
    """Monitors and logs performance metrics."""
    
    @staticmethod
    def time_function(func_name: str = None):
        """Decorator to time function execution."""
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    execution_time = time.time() - start_time
                    logger.info(f"Function {func_name or func.__name__} executed in {execution_time:.3f}s")
                    return result
                except Exception as e:
                    execution_time = time.time() - start_time
                    logger.error(f"Function {func_name or func.__name__} failed after {execution_time:.3f}s: {str(e)}")
                    raise
            return wrapper
        return decorator
    
    @staticmethod
    @contextmanager
    def time_block(operation_name: str):
        """Context manager to time code blocks."""
        start_time = time.time()
        try:
            yield
        finally:
            execution_time = time.time() - start_time
            logger.info(f"Operation '{operation_name}' completed in {execution_time:.3f}s")


class CacheOptimizer:
    """Optimizes caching strategies for better performance."""
    
    @staticmethod
    def get_cache_key(prefix: str, *args, **kwargs) -> str:
        """Generate consistent cache keys."""
        key_parts = [prefix]
        key_parts.extend(str(arg) for arg in args)
        key_parts.extend(f"{k}:{v}" for k, v in sorted(kwargs.items()))
        return ":".join(key_parts)
    
    @staticmethod
    def cache_with_ttl(cache_instance, key: str, ttl: int):
        """Decorator for caching function results with TTL."""
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                try:
                    # Try to get from cache first
                    cached_result = cache_instance.get(key)
                    if cached_result is not None:
                        logger.debug(f"Cache hit for key: {key}")
                        return cached_result
                    
                    # Execute function and cache result
                    result = func(*args, **kwargs)
                    cache_instance.set(key, result, ttl)
                    logger.debug(f"Cache set for key: {key} with TTL: {ttl}s")
                    return result
                    
                except Exception as e:
                    logger.error(f"Cache operation failed for key {key}: {str(e)}")
                    # Fall back to executing function without cache
                    return func(*args, **kwargs)
            return wrapper
        return decorator


class PayloadOptimizer:
    """Optimizes API payload sizes and data transfer."""
    
    @staticmethod
    def compress_player_data(players: List[Dict]) -> List[Dict]:
        """Compress player data by removing unnecessary fields and optimizing structure."""
        compressed = []
        
        for player in players:
            # Only include essential fields
            compressed_player = {
                'id': player.get('governor_id', ''),
                'name': player.get('player_name', ''),
                'alliance': player.get('alliance', ''),
                'power': player.get('current_power', 0),
                'kp': player.get('current_kp', 0),
                'dead': player.get('current_dead', 0),
                # Deltas
                'p_delta': player.get('power_delta', 0),
                'kp_delta': player.get('kp_delta', 0),
                'd_delta': player.get('dead_delta', 0),
                # Flags
                'new': player.get('is_new_player', False),
                'zeroed': player.get('is_zeroed', False)
            }
            
            # Only include non-zero deltas to reduce payload size
            if compressed_player['p_delta'] == 0:
                del compressed_player['p_delta']
            if compressed_player['kp_delta'] == 0:
                del compressed_player['kp_delta']
            if compressed_player['d_delta'] == 0:
                del compressed_player['d_delta']
            
            compressed.append(compressed_player)
        
        return compressed
    
    @staticmethod
    def paginate_large_datasets(data: List[Any], page: int = 1, page_size: int = 50) -> Dict[str, Any]:
        """Paginate large datasets to reduce payload size."""
        total_items = len(data)
        total_pages = (total_items + page_size - 1) // page_size
        
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        
        paginated_data = data[start_idx:end_idx]
        
        return {
            'data': paginated_data,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_items': total_items,
                'total_pages': total_pages,
                'has_next': page < total_pages,
                'has_prev': page > 1
            }
        }


class MemoryOptimizer:
    """Optimizes memory usage and prevents memory leaks."""
    
    @staticmethod
    def process_large_dataset_in_chunks(data: List[Any], chunk_size: int = 1000, processor_func: Callable = None):
        """Process large datasets in chunks to prevent memory issues."""
        results = []
        
        for i in range(0, len(data), chunk_size):
            chunk = data[i:i + chunk_size]
            
            if processor_func:
                processed_chunk = processor_func(chunk)
                results.extend(processed_chunk)
            else:
                results.extend(chunk)
            
            # Force garbage collection after each chunk
            import gc
            gc.collect()
        
        return results
    
    @staticmethod
    def optimize_dataframe_memory(df):
        """Optimize pandas DataFrame memory usage."""
        import pandas as pd
        
        # Convert object columns to category if they have few unique values
        for col in df.select_dtypes(include=['object']).columns:
            if df[col].nunique() / len(df) < 0.5:  # Less than 50% unique values
                df[col] = df[col].astype('category')
        
        # Downcast numeric columns
        for col in df.select_dtypes(include=['int64']).columns:
            df[col] = pd.to_numeric(df[col], downcast='integer')
        
        for col in df.select_dtypes(include=['float64']).columns:
            df[col] = pd.to_numeric(df[col], downcast='float')
        
        return df


class DatabaseOptimizer:
    """Optimizes database operations and connection management."""
    
    @staticmethod
    def optimize_query_plan(db: Session, query_text: str) -> str:
        """Analyze and optimize query execution plan."""
        # For PostgreSQL
        if 'postgresql' in str(db.bind.url):
            explain_query = f"EXPLAIN ANALYZE {query_text}"
            result = db.execute(text(explain_query))
            return '\n'.join([row[0] for row in result])
        
        # For SQLite
        elif 'sqlite' in str(db.bind.url):
            explain_query = f"EXPLAIN QUERY PLAN {query_text}"
            result = db.execute(text(explain_query))
            return '\n'.join([f"{row[0]}|{row[1]}|{row[2]}|{row[3]}" for row in result])
        
        return "Query plan analysis not supported for this database"
    
    @staticmethod
    def create_performance_indexes(db: Session):
        """Create additional performance indexes if they don't exist."""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_player_stats_composite ON player_stats(scan_id, player_id, power, total_kill_points)",
            "CREATE INDEX IF NOT EXISTS idx_delta_stats_performance ON delta_stats(end_scan_id, kill_points_delta, power_delta)",
            "CREATE INDEX IF NOT EXISTS idx_scans_timestamp_kvk ON scans(timestamp DESC, kvk_id, is_baseline)",
            "CREATE INDEX IF NOT EXISTS idx_players_governor_alliance ON players(governor_id, alliance)",
        ]
        
        for index_sql in indexes:
            try:
                db.execute(text(index_sql))
                logger.info(f"Created index: {index_sql}")
            except Exception as e:
                logger.warning(f"Index creation failed (may already exist): {str(e)}")
        
        db.commit()


# Performance monitoring decorators
def monitor_performance(operation_name: str = None):
    """Decorator to monitor function performance."""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            with PerformanceMonitor.time_block(operation_name or func.__name__):
                return func(*args, **kwargs)
        return wrapper
    return decorator


def optimize_database_query(func: Callable) -> Callable:
    """Decorator to optimize database queries."""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Add query optimization logic here
        start_time = time.time()
        result = func(*args, **kwargs)
        execution_time = time.time() - start_time
        
        if execution_time > 1.0:  # Log slow queries
            logger.warning(f"Slow query detected in {func.__name__}: {execution_time:.3f}s")
        
        return result
    return wrapper


# Export optimized functions
__all__ = [
    'QueryOptimizer',
    'PerformanceMonitor', 
    'CacheOptimizer',
    'PayloadOptimizer',
    'MemoryOptimizer',
    'DatabaseOptimizer',
    'monitor_performance',
    'optimize_database_query'
]
