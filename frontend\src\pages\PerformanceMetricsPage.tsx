import React, { useState, useMemo } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import ChartCard from '../components/ChartCard';
import Leaderboard from '../components/Leaderboard';
import { useDashboardData } from '../hooks/useDashboardData';
import { getPerformance7DaysSummary } from '../api/api';
import { useQuery } from '@tanstack/react-query';
import {
  FaChartLine,
  FaCrosshairs,
  FaSkullCrossbones,
  FaUsers,
  FaEye,
  FaEyeSlash,
  FaTrophy,
  FaExclamationTriangle,
  FaShieldAlt
} from 'react-icons/fa';

const PerformanceMetricsPage: React.FC = () => {
  const { theme } = useTheme();
  const [showUnderperforming, setShowUnderperforming] = useState<boolean>(false);

  // Get data from dashboard hook
  const {
    scans,
    isLoadingScans,
    scansError,
    topKillsPlayers,
    topT45KillsPlayers,
    topDeadsPlayers,
    topPowerPlayers,
    // Real performance data
    performanceData,
    isLoadingPerformance,
    performanceError,
    // Real dashboard data
    dashboardData,
    isLoadingDashboard,
    dashboardError
  } = useDashboardData();

  // Get 7-day performance summary for accurate "last 7 days" calculations
  const { data: performance7DaysData, isLoading: isLoading7Days, error: error7Days } = useQuery({
    queryKey: ['performance7Days'],
    queryFn: getPerformance7DaysSummary,
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });

  // Format large numbers
  const formatNumber = (num: number): string => {
    if (num >= 1000000000) {
      return `${(num / 1000000000).toFixed(1)}B`;
    }
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  // Get latest scan for kingdom stats
  const latestScan = scans.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0];

  // Calculate kingdom stats using dashboard data for totals and 7-day data for gains
  const kingdomStats = {
    totalKillPoints: dashboardData?.total_kill_points || 0,
    totalDeads: dashboardData?.total_deads || 0,
    totalT45Kills: dashboardData?.total_t45_kills || 0,
    totalPlayers: dashboardData?.player_count || 0,
    activePlayers: (dashboardData?.player_count || 0) - (dashboardData?.players_left || 0),
    // Use 7-day data for accurate "last 7 days" gains
    killPointsGain: performance7DaysData?.kill_points_gain || 0,
    deadsGain: performance7DaysData?.deads_gain || 0,
    t45KillsGain: performance7DaysData?.t45_kills_gain || 0,
    newPlayers: performance7DaysData?.new_players || 0,
    playersLeft: performance7DaysData?.players_left || 0
  };

  // Generate grade distribution data based on kill points gains
  const gradeDistribution = useMemo(() => {
    if (!performanceData || !performanceData.performance_data || !Array.isArray(performanceData.performance_data)) {
      console.log('No performance data for grade distribution:', performanceData);
      return [
        { name: 'S (>100M)', value: 0 },
        { name: 'A (50-100M)', value: 0 },
        { name: 'B (20-50M)', value: 0 },
        { name: 'C (10-20M)', value: 0 },
        { name: 'D (<10M)', value: 0 }
      ];
    }

    console.log('Calculating grade distribution for', performanceData.performance_data.length, 'players');
    console.log('Sample player data:', performanceData.performance_data[0]);

    const distribution = [
      { name: 'S (>100M)', value: performanceData.performance_data.filter((p: any) => (p.kp_delta || 0) > 100000000).length },
      { name: 'A (50-100M)', value: performanceData.performance_data.filter((p: any) => (p.kp_delta || 0) > 50000000 && (p.kp_delta || 0) <= 100000000).length },
      { name: 'B (20-50M)', value: performanceData.performance_data.filter((p: any) => (p.kp_delta || 0) > 20000000 && (p.kp_delta || 0) <= 50000000).length },
      { name: 'C (10-20M)', value: performanceData.performance_data.filter((p: any) => (p.kp_delta || 0) > 10000000 && (p.kp_delta || 0) <= 20000000).length },
      { name: 'D (<10M)', value: performanceData.performance_data.filter((p: any) => (p.kp_delta || 0) <= 10000000).length }
    ];

    console.log('Grade distribution:', distribution);
    return distribution;
  }, [performanceData]);

  // Generate power distribution data
  const powerDistribution = useMemo(() => {
    if (!performanceData || !performanceData.performance_data || !Array.isArray(performanceData.performance_data)) {
      console.log('No performance data for power distribution:', performanceData);
      return [
        { name: 'Gained Power', value: 0 },
        { name: 'Lost Power', value: 0 },
        { name: 'No Change', value: 0 }
      ];
    }

    console.log('Calculating power distribution for', performanceData.performance_data.length, 'players');
    console.log('Sample power deltas:', performanceData.performance_data.slice(0, 5).map((p: any) => ({ name: p.player_name, power_delta: p.power_delta })));

    const distribution = [
      { name: 'Gained Power', value: performanceData.performance_data.filter((p: any) => (p.power_delta || 0) > 0).length },
      { name: 'Lost Power', value: performanceData.performance_data.filter((p: any) => (p.power_delta || 0) < 0).length },
      { name: 'No Change', value: performanceData.performance_data.filter((p: any) => (p.power_delta || 0) === 0).length }
    ];

    console.log('Power distribution:', distribution);
    return distribution;
  }, [performanceData]);

  // Get top power losses (most negative power deltas) for Performance Page
  const topPowerLosses = useMemo(() => {
    if (!performanceData || !performanceData.performance_data || !Array.isArray(performanceData.performance_data)) {
      console.log('No performance data for power losses calculation');
      return [];
    }

    console.log('Calculating power losses from', performanceData.performance_data.length, 'players');
    const powerLosses = performanceData.performance_data
      .filter((player: any) => (player.power_delta || 0) < 0) // Only players who lost power
      .map((player: any) => ({
        governorId: player.governor_id || player.player_id?.toString() || '',
        name: player.player_name || 'Unknown',
        alliance: player.alliance || '',
        powerLoss: Math.abs(player.power_delta || 0) // Convert to positive for display
      }))
      .sort((a, b) => b.powerLoss - a.powerLoss) // Sort by biggest losses first (descending)
      .slice(0, 10);

    console.log('Found', powerLosses.length, 'players with power losses');
    console.log('Sample power losses:', powerLosses.slice(0, 3));
    return powerLosses;
  }, [performanceData]);

  // Get underperforming players (kill points delta = 0, no change) from all players
  const underperformingPlayers = useMemo(() => {
    if (!performanceData || !performanceData.performance_data || !Array.isArray(performanceData.performance_data)) {
      console.log('No performance data for underperforming calculation');
      return [];
    }

    console.log('Calculating underperforming players from', performanceData.performance_data.length, 'players');
    const underperforming = performanceData.performance_data
      .filter((player: any) => (player.kp_delta || 0) === 0) // Only players with zero kill points change
      .map((player: any) => ({
        governorId: player.governor_id || player.player_id?.toString() || '',
        name: player.player_name || 'Unknown',
        alliance: player.alliance || '',
        killPointsGain: player.kp_delta || 0
      }))
      .sort((a, b) => a.name.localeCompare(b.name)); // Sort alphabetically by name

    console.log('Found', underperforming.length, 'underperforming players (zero KP change)');
    console.log('Sample underperforming players:', underperforming.slice(0, 3));
    return underperforming;
  }, [performanceData]);

  if (isLoadingScans || isLoadingDashboard || isLoadingPerformance || isLoading7Days) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading performance data...</p>
        </div>
      </div>
    );
  }

  if (scansError || dashboardError || performanceError || error7Days) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center text-red-600">
          <p>Error loading performance data</p>
          <p className="text-sm mt-2">{(scansError || dashboardError || performanceError || error7Days)?.message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      theme === 'light' ? 'bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50' : 'bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900'
    }`}>
      <div className="max-w-7xl mx-auto p-6">
        {/* Enhanced Header */}
        <div className={`relative overflow-hidden rounded-3xl mb-8 ${
          theme === 'light'
            ? 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 border border-blue-100'
            : 'bg-gradient-to-br from-gray-800 via-blue-900 to-indigo-900 border border-gray-700'
        }`}>
          <div className="relative px-8 py-12">
            <div className="flex items-center mb-4">
              <div className={`p-3 rounded-xl mr-4 ${
                theme === 'light' ? 'bg-blue-100 text-blue-600' : 'bg-blue-900 text-blue-400'
              }`}>
                <FaChartLine className="text-2xl" />
              </div>
              <div>
                <h1 className={`text-4xl font-bold ${
                  theme === 'light' ? 'text-gray-900' : 'text-white'
                }`}>
                  Performance Analytics
                </h1>
                <p className={`text-lg mt-2 ${
                  theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                }`}>
                  Comprehensive analysis of Kingdom 2358 player performance and battle statistics
                </p>
              </div>
            </div>

            {/* Performance Indicators */}
            <div className="flex items-center space-x-6 mt-6">
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full mr-2 ${
                  theme === 'light' ? 'bg-green-500' : 'bg-green-400'
                }`}></div>
                <span className={`text-sm font-medium ${
                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                }`}>
                  Live Data
                </span>
              </div>
              <div className="flex items-center">
                <FaTrophy className={`text-sm mr-2 ${
                  theme === 'light' ? 'text-yellow-600' : 'text-yellow-400'
                }`} />
                <span className={`text-sm font-medium ${
                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                }`}>
                  Top Performers
                </span>
              </div>
              <div className="flex items-center">
                <FaShieldAlt className={`text-sm mr-2 ${
                  theme === 'light' ? 'text-blue-600' : 'text-blue-400'
                }`} />
                <span className={`text-sm font-medium ${
                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                }`}>
                  Battle Analytics
                </span>
              </div>
            </div>
          </div>

          {/* Decorative Elements */}
          <div className="absolute top-0 right-0 -mt-4 -mr-4 w-24 h-24 opacity-10">
            <FaChartLine className="w-full h-full transform rotate-12" />
          </div>
        </div>

        {/* Kingdom Overview */}
        <section className="mb-10">
          <div className="flex items-center mb-8">
            <div className={`p-2 rounded-lg mr-3 ${
              theme === 'light' ? 'bg-blue-100 text-blue-600' : 'bg-blue-900 text-blue-400'
            }`}>
              <FaShieldAlt className="text-lg" />
            </div>
            <h2 className={`text-3xl font-bold ${
              theme === 'light' ? 'text-gray-900' : 'text-white'
            }`}>
              Kingdom Overview
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Kill Points Card */}
            <div className={`group relative overflow-hidden p-6 rounded-2xl shadow-xl transition-all duration-300 hover:scale-105 hover:shadow-2xl ${
              theme === 'light'
                ? 'bg-gradient-to-br from-blue-50 to-indigo-100 hover:from-blue-100 hover:to-indigo-200'
                : 'bg-gradient-to-br from-blue-900 to-indigo-900 hover:from-blue-800 hover:to-indigo-800'
            }`}>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center mb-3">
                    <FaCrosshairs className={`text-lg mr-2 ${
                      theme === 'light' ? 'text-blue-600' : 'text-blue-400'
                    }`} />
                    <h3 className={`text-sm font-semibold uppercase tracking-wider ${
                      theme === 'light' ? 'text-blue-700' : 'text-blue-300'
                    }`}>
                      Total Kill Points
                    </h3>
                  </div>
                  <p className={`text-3xl font-bold mb-2 ${
                    theme === 'light' ? 'text-gray-900' : 'text-white'
                  }`}>
                    {formatNumber(kingdomStats.totalKillPoints)}
                  </p>
                  <div className="flex items-center">
                    <div className="flex items-center px-2 py-1 rounded-full bg-green-100 text-green-700 text-xs font-semibold">
                      <span>+{formatNumber(kingdomStats.killPointsGain)}</span>
                    </div>
                    <span className={`text-xs ml-2 ${
                      theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                    }`}>
                      last 7 days
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Dead Troops Card */}
            <div className={`group relative overflow-hidden p-6 rounded-2xl shadow-xl transition-all duration-300 hover:scale-105 hover:shadow-2xl ${
              theme === 'light'
                ? 'bg-gradient-to-br from-red-50 to-pink-100 hover:from-red-100 hover:to-pink-200'
                : 'bg-gradient-to-br from-red-900 to-pink-900 hover:from-red-800 hover:to-pink-800'
            }`}>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center mb-3">
                    <FaSkullCrossbones className={`text-lg mr-2 ${
                      theme === 'light' ? 'text-red-600' : 'text-red-400'
                    }`} />
                    <h3 className={`text-sm font-semibold uppercase tracking-wider ${
                      theme === 'light' ? 'text-red-700' : 'text-red-300'
                    }`}>
                      Total Dead Troops
                    </h3>
                  </div>
                  <p className={`text-3xl font-bold mb-2 ${
                    theme === 'light' ? 'text-gray-900' : 'text-white'
                  }`}>
                    {formatNumber(kingdomStats.totalDeads)}
                  </p>
                  <div className="flex items-center">
                    <div className="flex items-center px-2 py-1 rounded-full bg-orange-100 text-orange-700 text-xs font-semibold">
                      <span>+{formatNumber(kingdomStats.deadsGain)}</span>
                    </div>
                    <span className={`text-xs ml-2 ${
                      theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                    }`}>
                      last 7 days
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* T4-5 Kills Card */}
            <div className={`group relative overflow-hidden p-6 rounded-2xl shadow-xl transition-all duration-300 hover:scale-105 hover:shadow-2xl ${
              theme === 'light'
                ? 'bg-gradient-to-br from-green-50 to-emerald-100 hover:from-green-100 hover:to-emerald-200'
                : 'bg-gradient-to-br from-green-900 to-emerald-900 hover:from-green-800 hover:to-emerald-800'
            }`}>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center mb-3">
                    <FaTrophy className={`text-lg mr-2 ${
                      theme === 'light' ? 'text-green-600' : 'text-green-400'
                    }`} />
                    <h3 className={`text-sm font-semibold uppercase tracking-wider ${
                      theme === 'light' ? 'text-green-700' : 'text-green-300'
                    }`}>
                      T4-5 Kills
                    </h3>
                  </div>
                  <p className={`text-3xl font-bold mb-2 ${
                    theme === 'light' ? 'text-gray-900' : 'text-white'
                  }`}>
                    {formatNumber(kingdomStats.totalT45Kills)}
                  </p>
                  <div className="flex items-center">
                    <div className="flex items-center px-2 py-1 rounded-full bg-green-100 text-green-700 text-xs font-semibold">
                      <span>+{formatNumber(kingdomStats.t45KillsGain)}</span>
                    </div>
                    <span className={`text-xs ml-2 ${
                      theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                    }`}>
                      last 7 days
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Active Players Card */}
            <div className={`group relative overflow-hidden p-6 rounded-2xl shadow-xl transition-all duration-300 hover:scale-105 hover:shadow-2xl ${
              theme === 'light'
                ? 'bg-gradient-to-br from-purple-50 to-violet-100 hover:from-purple-100 hover:to-violet-200'
                : 'bg-gradient-to-br from-purple-900 to-violet-900 hover:from-purple-800 hover:to-violet-800'
            }`}>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center mb-3">
                    <FaUsers className={`text-lg mr-2 ${
                      theme === 'light' ? 'text-purple-600' : 'text-purple-400'
                    }`} />
                    <h3 className={`text-sm font-semibold uppercase tracking-wider ${
                      theme === 'light' ? 'text-purple-700' : 'text-purple-300'
                    }`}>
                      Active Players
                    </h3>
                  </div>
                  <p className={`text-3xl font-bold mb-2 ${
                    theme === 'light' ? 'text-gray-900' : 'text-white'
                  }`}>
                    {kingdomStats.activePlayers}/{kingdomStats.totalPlayers}
                  </p>
                  <div className="flex items-center">
                    <div className="flex items-center px-2 py-1 rounded-full bg-purple-100 text-purple-700 text-xs font-semibold">
                      <span>{kingdomStats.totalPlayers > 0 ? Math.round(kingdomStats.activePlayers / kingdomStats.totalPlayers * 100) : 0}%</span>
                    </div>
                    <span className={`text-xs ml-2 ${
                      theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                    }`}>
                      participation
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Performance Charts */}
        <section className="mb-10">
          <div className="flex items-center mb-8">
            <div className={`p-2 rounded-lg mr-3 ${
              theme === 'light' ? 'bg-green-100 text-green-600' : 'bg-green-900 text-green-400'
            }`}>
              <FaChartLine className="text-lg" />
            </div>
            <h2 className={`text-3xl font-bold ${
              theme === 'light' ? 'text-gray-900' : 'text-white'
            }`}>
              Performance Analysis
            </h2>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ChartCard
              title="Kill Points Performance Distribution"
              data={gradeDistribution}
              type="bar"
            />
            <ChartCard
              title="Power Change Distribution"
              data={powerDistribution}
              type="bar"
            />
          </div>
        </section>

        {/* Top Performers */}
        <section className="mb-10">
          <div className="flex items-center mb-8">
            <div className={`p-2 rounded-lg mr-3 ${
              theme === 'light' ? 'bg-yellow-100 text-yellow-600' : 'bg-yellow-900 text-yellow-400'
            }`}>
              <FaTrophy className="text-lg" />
            </div>
            <h2 className={`text-3xl font-bold ${
              theme === 'light' ? 'text-gray-900' : 'text-white'
            }`}>
              Top Performers
            </h2>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <Leaderboard
              title="Top Kill Points Gains"
              players={topKillsPlayers}
              valueKey="killPointsGain"
              valueFormatter={(val) => formatNumber(val)}
            />
            <div>
              {/* Custom Power Losses Display */}
              <div className={`rounded-2xl overflow-hidden shadow-2xl transition-all duration-300 hover:shadow-3xl ${
                theme === 'light' ? 'bg-white' : 'bg-gray-800'
              }`}>
                <div className={`px-6 py-4 ${
                  theme === 'light'
                    ? 'bg-gradient-to-r from-red-600 to-pink-600'
                    : 'bg-gradient-to-r from-red-700 to-pink-700'
                }`}>
                  <div className="flex items-center">
                    <div className="p-2 rounded-lg bg-white/20 backdrop-blur-sm mr-3">
                      <FaShieldAlt className="text-lg text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white">Top Power Losses</h3>
                  </div>
                </div>
                <div className="p-6 space-y-3">
                  {topPowerLosses.length === 0 ? (
                    <div className={`text-center py-8 ${
                      theme === 'light' ? 'text-gray-500' : 'text-gray-400'
                    }`}>
                      <FaShieldAlt className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-semibold">No power losses recorded</p>
                      <p className="text-sm">All players maintained or gained power</p>
                    </div>
                  ) : (
                    topPowerLosses.map((player: any, index: number) => (
                      <div
                        key={player.governorId || index}
                        className={`group relative overflow-hidden rounded-xl p-4 transition-all duration-300 hover:scale-102 ${
                          theme === 'light'
                            ? 'bg-red-50 hover:bg-red-100 border border-red-200 hover:border-red-300'
                            : 'bg-red-900/20 hover:bg-red-900/30 border border-red-800 hover:border-red-700'
                        } shadow-lg hover:shadow-xl`}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                              theme === 'light' ? 'bg-red-200 text-red-700' : 'bg-red-800 text-red-300'
                            }`}>
                              <span className="text-sm font-bold">{index + 1}</span>
                            </div>
                            <div>
                              <h4 className={`text-lg font-bold ${
                                theme === 'light' ? 'text-gray-900' : 'text-white'
                              }`}>
                                {player.name}
                              </h4>
                              <p className={`text-sm ${
                                theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                              }`}>
                                [{player.alliance || 'No Alliance'}]
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className={`text-2xl font-bold ${
                              theme === 'light' ? 'text-red-600' : 'text-red-400'
                            }`}>
                              -{formatNumber(player.powerLoss)}
                            </p>
                            <p className={`text-xs ${
                              theme === 'light' ? 'text-gray-500' : 'text-gray-400'
                            }`}>
                              Power Lost
                            </p>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
              <div className={`mt-3 p-3 rounded-lg border-l-4 border-blue-400 ${
                theme === 'dark' ? 'bg-blue-900/20 text-blue-300' : 'bg-blue-50 text-blue-700'
              }`}>
                <p className="text-sm flex items-center gap-2">
                  <FaShieldAlt className="h-4 w-4" />
                  <span><strong>Note:</strong> All statistics and leaderboards are subject to change as new scans are submitted.</span>
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Underperforming Players */}
        <section className="mb-10">
          <div className="flex justify-between items-center mb-8">
            <div className="flex items-center">
              <div className={`p-2 rounded-lg mr-3 ${
                theme === 'light' ? 'bg-red-100 text-red-600' : 'bg-red-900 text-red-400'
              }`}>
                <FaExclamationTriangle className="text-lg" />
              </div>
              <h2 className={`text-3xl font-bold ${
                theme === 'light' ? 'text-gray-900' : 'text-white'
              }`}>
                Player Activity Status
              </h2>
            </div>
            <button
              onClick={() => setShowUnderperforming(!showUnderperforming)}
              className={`group relative inline-flex items-center px-6 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 ${
                showUnderperforming
                  ? 'bg-gradient-to-r from-red-600 to-pink-600 text-white hover:from-red-700 hover:to-pink-700 shadow-lg'
                  : theme === 'light'
                  ? 'bg-gradient-to-r from-gray-200 to-gray-300 text-gray-800 hover:from-gray-300 hover:to-gray-400 shadow-lg'
                  : 'bg-gradient-to-r from-gray-700 to-gray-800 text-gray-200 hover:from-gray-600 hover:to-gray-700 shadow-lg'
              }`}
            >
              {showUnderperforming ? (
                <>
                  <FaEyeSlash className="mr-2 text-sm" />
                  Hide Underperforming
                </>
              ) : (
                <>
                  <FaEye className="mr-2 text-sm" />
                  Show Underperforming
                </>
              )}
            </button>
          </div>

          {showUnderperforming && (
            <div className={`rounded-2xl shadow-xl overflow-hidden ${
              theme === 'light' ? 'bg-white' : 'bg-gray-800'
            }`}>
              <div className={`px-6 py-4 ${
                theme === 'light'
                  ? 'bg-gradient-to-r from-red-50 to-pink-50 border-b border-red-100'
                  : 'bg-gradient-to-r from-red-900/20 to-pink-900/20 border-b border-red-800'
              }`}>
                <div className="flex items-center">
                  <FaExclamationTriangle className={`text-lg mr-3 ${
                    theme === 'light' ? 'text-red-600' : 'text-red-400'
                  }`} />
                  <div>
                    <h3 className={`text-lg font-bold ${
                      theme === 'light' ? 'text-red-800' : 'text-red-300'
                    }`}>
                      Underperforming Players
                    </h3>
                    <p className={`text-sm ${
                      theme === 'light' ? 'text-red-600' : 'text-red-400'
                    }`}>
                      Players with no kill points change since last scan
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <div className="space-y-3">
                  {underperformingPlayers.length > 0 ? underperformingPlayers
                    .slice(0, 10)
                    .map((player, index) => (
                      <div
                        key={player.governorId}
                        className={`group flex justify-between items-center p-4 rounded-xl transition-all duration-300 hover:scale-102 ${
                          theme === 'light'
                            ? 'bg-red-50 hover:bg-red-100 border border-red-100 hover:border-red-200'
                            : 'bg-red-900/20 hover:bg-red-900/30 border border-red-800 hover:border-red-700'
                        }`}
                      >
                        <div className="flex items-center">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                            theme === 'light' ? 'bg-red-200 text-red-700' : 'bg-red-800 text-red-300'
                          }`}>
                            <span className="text-sm font-bold">{index + 1}</span>
                          </div>
                          <div>
                            <span className={`font-semibold ${
                              theme === 'light' ? 'text-gray-900' : 'text-gray-100'
                            }`}>
                              {player.name}
                            </span>
                            <div className={`text-sm ${
                              theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                            }`}>
                              [{player.alliance || 'No Alliance'}]
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className={`text-lg font-bold ${
                            theme === 'light' ? 'text-red-600' : 'text-red-400'
                          }`}>
                            0
                          </div>
                          <div className={`text-xs ${
                            theme === 'light' ? 'text-gray-500' : 'text-gray-400'
                          }`}>
                            No activity
                          </div>
                        </div>
                      </div>
                    )) : (
                      <div className={`text-center py-8 ${
                        theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                      }`}>
                        <FaTrophy className="h-12 w-12 mx-auto mb-4 text-green-500" />
                        <p className="text-lg font-semibold">Excellent!</p>
                        <p>All players have gained kill points since the last scan.</p>
                      </div>
                    )}
                </div>
              </div>
            </div>
          )}
        </section>
      </div>
    </div>
  );
};

export default PerformanceMetricsPage;
