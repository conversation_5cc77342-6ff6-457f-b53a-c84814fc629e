from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import crud, schemas, services, models
from database import get_db
from logging_config import get_logger

router = APIRouter(
    prefix="/dashboard",
    tags=["Dashboard"],
    responses={404: {"description": "Not found"}},
)

logger = get_logger("dashboard")

@router.get("/", response_model=Dict[str, Any])
def get_dashboard_data(db: Session = Depends(get_db)):
    """
    Get comprehensive dashboard data including KvK stats, player performance, and summary metrics.
    """
    try:
        # Get latest and baseline scans
        latest_scan = crud.get_latest_scan(db)
        baseline_scan = crud.get_baseline_scan(db)

        if not latest_scan:
            raise HTTPException(status_code=404, detail="No scans found")

        if not baseline_scan:
            raise HTTPException(status_code=404, detail="No baseline scan found")

        # Initialize response data
        dashboard_data = {
            "total_kill_points": 0,
            "total_power": 0,
            "total_deads": 0,
            "total_t45_kills": 0,
            "kill_points_gain": 0,
            "power_gain": 0,
            "deads_gain": 0,
            "t45_kills_gain": 0,
            "player_count": 0,
            "new_players": 0,
            "players_left": 0,
            "scan_info": {
                "latest_scan": {
                    "id": latest_scan.id,
                    "name": latest_scan.name,
                    "timestamp": latest_scan.timestamp.isoformat() if latest_scan.timestamp else None
                },
                "baseline_scan": {
                    "id": baseline_scan.id,
                    "name": baseline_scan.name,
                    "timestamp": baseline_scan.timestamp.isoformat() if baseline_scan.timestamp else None
                }
            }
        }

        # Calculate current totals from latest scan
        if latest_scan.player_stats:
            dashboard_data["total_kill_points"] = sum(stat.total_kill_points or 0 for stat in latest_scan.player_stats)
            dashboard_data["total_power"] = sum(stat.power or 0 for stat in latest_scan.player_stats)
            dashboard_data["total_deads"] = sum(stat.dead_troops or 0 for stat in latest_scan.player_stats)
            dashboard_data["total_t45_kills"] = sum((stat.kill_points_t4 or 0) + (stat.kill_points_t5 or 0) for stat in latest_scan.player_stats)
            dashboard_data["player_count"] = len(latest_scan.player_stats)
            logger.info(f"Latest scan stats: {dashboard_data['player_count']} players, KP={dashboard_data['total_kill_points']:,}")
        else:
            logger.warning(f"Latest scan {latest_scan.id} has no player stats")

        # Calculate gains if we have both baseline and latest scans and they're different
        if baseline_scan.id != latest_scan.id:
            # Get delta stats
            delta_stats = crud.get_delta_stats_for_scan_pair(db, baseline_scan.id, latest_scan.id)

            if delta_stats:
                # Only sum positive gains for the dashboard display
                dashboard_data["kill_points_gain"] = sum(ds.kill_points_delta for ds in delta_stats if (ds.kill_points_delta or 0) > 0)
                dashboard_data["power_gain"] = sum(ds.power_delta for ds in delta_stats if (ds.power_delta or 0) > 0)
                dashboard_data["deads_gain"] = sum(ds.dead_troops_delta for ds in delta_stats if (ds.dead_troops_delta or 0) > 0)

                # Count new players and players who left
                dashboard_data["new_players"] = len([ds for ds in delta_stats if getattr(ds, 'is_new_player', False)])
                dashboard_data["players_left"] = len([ds for ds in delta_stats if getattr(ds, 'player_left_kingdom', False)])
            else:
                # If no delta stats exist, try to calculate them
                logger.info("No delta stats found, attempting to calculate them")
                services.calculate_delta_stats_for_scan_pair(db, baseline_scan.id, latest_scan.id)

                # Try again
                delta_stats = crud.get_delta_stats_for_scan_pair(db, baseline_scan.id, latest_scan.id)
                if delta_stats:
                    # Only sum positive gains for the dashboard display
                    dashboard_data["kill_points_gain"] = sum(ds.kill_points_delta for ds in delta_stats if (ds.kill_points_delta or 0) > 0)
                    dashboard_data["power_gain"] = sum(ds.power_delta for ds in delta_stats if (ds.power_delta or 0) > 0)
                    dashboard_data["deads_gain"] = sum(ds.dead_troops_delta for ds in delta_stats if (ds.dead_troops_delta or 0) > 0)
                    dashboard_data["new_players"] = len([ds for ds in delta_stats if getattr(ds, 'is_new_player', False)])
                    dashboard_data["players_left"] = len([ds for ds in delta_stats if getattr(ds, 'player_left_kingdom', False)])

        # Calculate baseline totals for reference
        baseline_totals = {
            "total_kill_points": 0,
            "total_power": 0,
            "total_deads": 0,
            "total_t45_kills": 0
        }

        if baseline_scan.player_stats:
            baseline_totals["total_kill_points"] = sum(stat.total_kill_points or 0 for stat in baseline_scan.player_stats)
            baseline_totals["total_power"] = sum(stat.power or 0 for stat in baseline_scan.player_stats)
            baseline_totals["total_deads"] = sum(stat.dead_troops or 0 for stat in baseline_scan.player_stats)
            baseline_totals["total_t45_kills"] = sum((stat.kill_points_t4 or 0) + (stat.kill_points_t5 or 0) for stat in baseline_scan.player_stats)

        dashboard_data["baseline_totals"] = baseline_totals

        logger.info(f"Dashboard data generated successfully: KP={dashboard_data['total_kill_points']:,}, Gain={dashboard_data['kill_points_gain']:,}")

        return dashboard_data

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error generating dashboard data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating dashboard data: {str(e)}")

@router.get("/summary", response_model=Dict[str, Any])
def get_dashboard_summary(db: Session = Depends(get_db)):
    """
    Get a quick summary of key dashboard metrics.
    """
    try:
        dashboard_data = get_dashboard_data(db)

        return {
            "total_kill_points": dashboard_data["total_kill_points"],
            "kill_points_gain": dashboard_data["kill_points_gain"],
            "player_count": dashboard_data["player_count"],
            "new_players": dashboard_data["new_players"],
            "scan_info": dashboard_data["scan_info"]
        }

    except Exception as e:
        logger.error(f"Error generating dashboard summary: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating dashboard summary: {str(e)}")

@router.get("/kingdom-overview", response_model=Dict[str, Any])
def get_kingdom_overview(db: Session = Depends(get_db)):
    """
    Get comprehensive kingdom overview with current stats and performance data.
    """
    try:
        return services.get_kingdom_overview_summary(db)
    except Exception as e:
        logger.error(f"Error generating kingdom overview: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating kingdom overview: {str(e)}")

@router.get("/performance", response_model=Dict[str, Any])
def get_general_performance(limit: int = 100, db: Session = Depends(get_db)):
    """
    Get general kingdom performance analysis (not tied to specific KvK).
    """
    try:
        return services.get_general_performance_summary(db, limit=limit)
    except Exception as e:
        logger.error(f"Error generating performance data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating performance data: {str(e)}")

@router.get("/performance-7days", response_model=Dict[str, Any])
def get_performance_7days_summary(db: Session = Depends(get_db)):
    """
    Get kingdom performance summary for last 7 days (for Performance page display).
    """
    try:
        # Get latest scan
        latest_scan = crud.get_latest_scan(db)
        if not latest_scan:
            raise HTTPException(status_code=404, detail="No scans found")

        # Get scan from 7 days ago
        seven_days_ago_scan = crud.get_scan_from_days_ago(db, days=7)

        # Initialize response data
        performance_data = {
            "total_kill_points": 0,
            "total_power": 0,
            "total_deads": 0,
            "total_t45_kills": 0,
            "kill_points_gain": 0,
            "power_gain": 0,
            "deads_gain": 0,
            "t45_kills_gain": 0,
            "player_count": 0,
            "new_players": 0,
            "players_left": 0,
            "timeframe": "last_7_days",
            "scan_info": {
                "latest_scan": {
                    "id": latest_scan.id,
                    "name": latest_scan.name,
                    "timestamp": latest_scan.timestamp.isoformat() if latest_scan.timestamp else None
                },
                "comparison_scan": None
            }
        }

        # Calculate current totals from latest scan
        if latest_scan.player_stats:
            performance_data["total_kill_points"] = sum(stat.total_kill_points or 0 for stat in latest_scan.player_stats)
            performance_data["total_power"] = sum(stat.power or 0 for stat in latest_scan.player_stats)
            performance_data["total_deads"] = sum(stat.dead_troops or 0 for stat in latest_scan.player_stats)
            performance_data["total_t45_kills"] = sum((stat.kill_points_t4 or 0) + (stat.kill_points_t5 or 0) for stat in latest_scan.player_stats)
            performance_data["player_count"] = len(latest_scan.player_stats)

        # Calculate 7-day gains if we have a scan from 7 days ago
        if seven_days_ago_scan and seven_days_ago_scan.id != latest_scan.id:
            performance_data["scan_info"]["comparison_scan"] = {
                "id": seven_days_ago_scan.id,
                "name": seven_days_ago_scan.name,
                "timestamp": seven_days_ago_scan.timestamp.isoformat() if seven_days_ago_scan.timestamp else None
            }

            # Get delta stats for last 7 days
            delta_stats = crud.get_delta_stats_for_scan_pair(db, seven_days_ago_scan.id, latest_scan.id)

            if delta_stats:
                # Sum ALL gains (positive values) for the kingdom
                performance_data["kill_points_gain"] = sum(ds.kill_points_delta for ds in delta_stats if (ds.kill_points_delta or 0) > 0)
                performance_data["power_gain"] = sum(ds.power_delta for ds in delta_stats if (ds.power_delta or 0) > 0)
                performance_data["deads_gain"] = sum(ds.dead_troops_delta for ds in delta_stats if (ds.dead_troops_delta or 0) > 0)

                # Calculate T4-5 kills gain
                t45_gain = 0
                for ds in delta_stats:
                    if hasattr(ds, 't4_delta') and hasattr(ds, 't5_delta'):
                        t45_delta = (ds.t4_delta or 0) + (ds.t5_delta or 0)
                        if t45_delta > 0:
                            t45_gain += t45_delta
                performance_data["t45_kills_gain"] = t45_gain

                # Count new players and players who left (in last 7 days)
                performance_data["new_players"] = len([ds for ds in delta_stats if getattr(ds, 'is_new_player', False)])
                performance_data["players_left"] = len([ds for ds in delta_stats if getattr(ds, 'player_left_kingdom', False)])
            else:
                # If no delta stats exist, try to calculate them
                logger.info("No delta stats found for 7-day window, attempting to calculate them")
                services.calculate_delta_stats_for_scan_pair(db, seven_days_ago_scan.id, latest_scan.id)

                # Try again
                delta_stats = crud.get_delta_stats_for_scan_pair(db, seven_days_ago_scan.id, latest_scan.id)
                if delta_stats:
                    # Sum ALL gains (positive values) for the kingdom
                    performance_data["kill_points_gain"] = sum(ds.kill_points_delta for ds in delta_stats if (ds.kill_points_delta or 0) > 0)
                    performance_data["power_gain"] = sum(ds.power_delta for ds in delta_stats if (ds.power_delta or 0) > 0)
                    performance_data["deads_gain"] = sum(ds.dead_troops_delta for ds in delta_stats if (ds.dead_troops_delta or 0) > 0)

                    # Calculate T4-5 kills gain
                    t45_gain = 0
                    for ds in delta_stats:
                        if hasattr(ds, 't4_delta') and hasattr(ds, 't5_delta'):
                            t45_delta = (ds.t4_delta or 0) + (ds.t5_delta or 0)
                            if t45_delta > 0:
                                t45_gain += t45_delta
                    performance_data["t45_kills_gain"] = t45_gain

                    performance_data["new_players"] = len([ds for ds in delta_stats if getattr(ds, 'is_new_player', False)])
                    performance_data["players_left"] = len([ds for ds in delta_stats if getattr(ds, 'player_left_kingdom', False)])

        logger.info(f"7-day performance data generated: KP gain={performance_data['kill_points_gain']:,}, Deads gain={performance_data['deads_gain']:,}")

        return performance_data

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error generating 7-day performance data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating 7-day performance data: {str(e)}")

@router.get("/validate-scan/{scan_id}", response_model=Dict[str, Any])
def validate_scan_data(scan_id: int, db: Session = Depends(get_db)):
    """
    Validate player ID consistency and detect potential data issues in a scan.
    """
    try:
        return services.validate_player_id_consistency(db, scan_id)
    except Exception as e:
        logger.error(f"Error validating scan {scan_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error validating scan: {str(e)}")

@router.get("/player-mapping/{scan_id}", response_model=Dict[str, Any])
def get_player_mapping(scan_id: int, db: Session = Depends(get_db)):
    """
    Get player ID to name mapping for validation purposes.
    """
    try:
        return services.get_player_id_mapping(db, scan_id)
    except Exception as e:
        logger.error(f"Error getting player mapping for scan {scan_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting player mapping: {str(e)}")
